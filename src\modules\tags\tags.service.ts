import { Injectable } from '@nestjs/common';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { TagsRepository } from './tags.repository';

@Injectable()
export class TagsService {
  constructor(private readonly tagsRepository: TagsRepository) {}

  create(createTagDto: CreateTagDto) {
    const slug = this.generateSlug(createTagDto.name);

    return this.tagsRepository.create({ name: createTagDto.name, slug });
  }

  findAll() {
    return this.tagsRepository.findAll();
  }

  findOne(id: number) {
    return this.tagsRepository.findOne(id);
  }

  update(id: number, updateTagDto: UpdateTagDto) {
    return this.tagsRepository.update(id, updateTagDto);
  }

  remove(id: number) {
    return this.tagsRepository.remove(id);
  }

  private generateSlug(name: string): string {
    return name.toLowerCase().replace(/\s+/g, '-');
  }
}
