import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/database/prisma.service';
import { Prisma } from 'generated/prisma';

@Injectable()
export class ArticlesRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(query: Prisma.ArticleFindManyArgs) {
    return this.prisma.article.findMany(query);
  }

  async count(where?: Prisma.ArticleWhereInput) {
    return this.prisma.article.count({ where });
  }

  async findOne(id: number) {
    return this.prisma.article.findUnique({
      where: { id },
      include: { categories: true, tags: true },
    });
  }

  async create(data: Prisma.ArticleCreateInput) {
    return this.prisma.article.create({
      data,
      include: { categories: true, tags: true },
    });
  }

  async update(id: number, data: Prisma.ArticleUpdateInput) {
    return this.prisma.article.update({
      where: { id },
      data,
      include: {
        categories: true,
        tags: true,
      },
    });
  }

  async remove(id: number) {
    return this.prisma.article.delete({ where: { id } });
  }
}
