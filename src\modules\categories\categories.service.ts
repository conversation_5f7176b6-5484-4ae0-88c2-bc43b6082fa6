import { Injectable } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoriesRepository } from './categories.repository';
import { Prisma } from 'generated/prisma';

@Injectable()
export class CategoriesService {
  constructor(private readonly categoriesRepository: CategoriesRepository) {}

  async create(dto: CreateCategoryDto) {
    const slug = this.generateSlug(dto.name);

    const data: Prisma.CategoryCreateInput = {
      name: dto.name,
      slug,
    };

    return this.categoriesRepository.create(data);
  }

  async findAll() {
    return this.categoriesRepository.findAll();
  }

  async findOne(id: number) {
    return this.categoriesRepository.findOne(id);
  }

  async update(id: number, dto: UpdateCategoryDto) {
    const data: Prisma.CategoryUpdateInput = {
      ...dto,
    };

    return this.categoriesRepository.update(id, data);
  }

  async remove(id: number) {
    return this.categoriesRepository.remove(id);
  }

  private generateSlug(name: string): string {
    return name.toLowerCase().replace(/\s+/g, '-');
  }
}
