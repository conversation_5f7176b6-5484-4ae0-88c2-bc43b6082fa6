import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, Min } from 'class-validator';

export class PaginationDto {
  @ApiPropertyOptional({ example: 10, description: 'Number of items per page' })
  @Type(() => Number)
  @IsOptional()
  @Min(1)
  limit: number = 10;

  @ApiPropertyOptional({ example: 0, description: 'Offset for items' })
  @Type(() => Number)
  @IsOptional()
  @Min(0)
  offset: number = 0;
}
