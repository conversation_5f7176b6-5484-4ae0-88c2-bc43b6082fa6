generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Article {
  id         Int        @id @default(autoincrement())
  content    String     @db.Text
  title      String     @db.Text
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  slug       String     @unique
  likes      Int        @default(0)
  viewCount  Int        @default(0)
  categories Category[] @relation("ArticleCategories")
  tags       Tag[]      @relation("ArticleTags")
}

model Category {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  slug      String    @unique
  articles  Article[] @relation("ArticleCategories")
}

model Tag {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  slug      String    @unique
  articles  Article[] @relation("ArticleTags")
}
