import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { map, Observable } from 'rxjs';

interface PaginationResponse<T> {
  data: T[];
  total: number;
  offset: number;
  limit: number;
}

@Injectable()
export class PaginationInterceptor<T>
  implements NestInterceptor<PaginationResponse<T>>
{
  intercept(_: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(({ data, total, offset, limit }: PaginationResponse<T>) => ({
        data,
        meta: {
          total,
          page: Math.floor(offset / limit) + 1,
          totalPages: Math.ceil(total / limit),
        },
      })),
    );
  }
}
