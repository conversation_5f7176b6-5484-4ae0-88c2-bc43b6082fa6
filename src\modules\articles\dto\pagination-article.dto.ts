import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from 'src/common/dto/pagination.dto';

export class PaginationArticleDto extends PaginationDto {
  @ApiPropertyOptional({ example: 'nestjs', description: 'Search term' })
  search?: string;

  @ApiPropertyOptional({ example: 'tech', description: 'Filter by category' })
  category?: string;

  @ApiPropertyOptional({ example: 'backend', description: 'Filter by tag' })
  tag?: string;
}
