import { Injectable } from '@nestjs/common';
import { CreateArticleDto } from './dto/create-article.dto';
import { UpdateArticleDto } from './dto/update-article.dto';
import { ArticlesRepository } from './articles.repository';
import { Prisma } from 'generated/prisma';
import { PaginationArticleDto } from './dto/pagination-article.dto';

@Injectable()
export class ArticlesService {
  constructor(private readonly articlesRepository: ArticlesRepository) {}

  async create(dto: CreateArticleDto) {
    const slug = this.generateSlug(dto.title);

    const data: Prisma.ArticleCreateInput = {
      title: dto.title,
      content: dto.content,
      slug,
      categories: dto.categories
        ? {
            connect: dto.categories.map((id) => ({ id })),
          }
        : undefined,
      tags: dto.tags
        ? {
            connect: dto.tags.map((id) => ({ id })),
          }
        : undefined,
    };

    return this.articlesRepository.create(data);
  }

  async findAll(query: PaginationArticleDto) {
    const where = {
      ...(query.search && { title: { contains: query.search } }),
      ...(query.category && { categories: { some: { name: query.category } } }),
      ...(query.tag && { tags: { some: { name: query.tag } } }),
    };

    const [data, total] = await Promise.all([
      this.articlesRepository.findAll({
        where,
        skip: query.offset,
        take: query.limit,
        include: { categories: true, tags: true },
      }),
      this.articlesRepository.count(where),
    ]);

    return {
      data,
      total,
      offset: query.offset,
      limit: query.limit,
    };
  }

  async findOne(id: number) {
    return this.articlesRepository.findOne(id);
  }

  async update(id: number, dto: UpdateArticleDto) {
    const data: Prisma.ArticleUpdateInput = {
      ...dto,
      categories: dto.categories
        ? { connect: dto.categories.map((id) => ({ id })) }
        : undefined,
      tags: dto.tags ? { connect: dto.tags.map((id) => ({ id })) } : undefined,
    };

    return this.articlesRepository.update(id, data);
  }

  async remove(id: number) {
    return this.articlesRepository.remove(id);
  }

  private generateSlug(title: string): string {
    return title.toLowerCase().replace(/\s+/g, '-');
  }
}
